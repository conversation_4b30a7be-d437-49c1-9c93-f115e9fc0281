/*
  Warnings:

  - Added the required column `date_cutoff` to the `subscribe` table without a default value. This is not possible if the table is not empty.
  - Added the required column `status_buy` to the `subscribe` table without a default value. This is not possible if the table is not empty.
  - Added the required column `total_price` to the `subscribe` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "public"."StatusBuy" AS ENUM ('IN_PROCESS', 'QUEUED_FOR_NEXT_DAY');

-- AlterTable
ALTER TABLE "public"."subscribe" ADD COLUMN     "date_cutoff" TIMESTAMPTZ NOT NULL,
ADD COLUMN     "status_buy" "public"."StatusBuy" NOT NULL,
ADD COLUMN     "total_price" DECIMAL(18,8) NOT NULL;
