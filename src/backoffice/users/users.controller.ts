import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { UsersService } from './users.service';
import { BaseResponseDto } from '../nav-management/dto/base-response.dto';
import { UsersListResponseDto } from './dto/users-list-response.dto';
import { UsersQueryDto } from './dto/users-query.dto';

@ApiTags('Backoffice - Users')
@Controller('backoffice/users')
export class UsersController {
  constructor(private readonly UsersService: UsersService) {}

  @Get()
  @ApiOperation({
    summary: 'Get all users',
    description:
      'Retrieve a paginated list of all users in the system for backoffice administration.',
  })
  @ApiResponse({
    status: 200,
    description: 'Users retrieved successfully',
    type: BaseResponseDto,
    schema: {
      allOf: [
        { $ref: '#/components/schemas/BaseResponseDto' },
        {
          properties: {
            data: {
              type: 'object',
              properties: {
                data: {
                  type: 'array',
                  items: { $ref: '#/components/schemas/UserResponseDto' },
                },
                meta: {
                  type: 'object',
                  properties: {
                    page: { type: 'number', example: 1 },
                    limit: { type: 'number', example: 10 },
                    total: { type: 'number', example: 25 },
                    totalPages: { type: 'number', example: 3 },
                    hasNext: { type: 'boolean', example: true },
                    hasPrev: { type: 'boolean', example: false },
                  },
                },
              },
            },
          },
        },
      ],
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (max 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: [
      'id',
      'email',
      'username',
      'type',
      'businessType',
      'status',
      'createdAt',
      'updatedAt',
    ],
    description: 'Field to sort by',
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order',
    example: 'desc',
  })
  async findAll(
    @Query() query: UsersQueryDto,
  ): Promise<BaseResponseDto<UsersListResponseDto>> {
    return await this.UsersService.findAll(query);
  }
}
