import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { BaseResponseDto } from '../nav-management/dto/base-response.dto';
import { UserResponseDto } from '../../users/dto/user-response.dto';
import { UsersQueryDto } from './dto/users-query.dto';
import {
  UsersListResponseDto,
  PaginationMetaDto,
} from './dto/users-list-response.dto';

@Injectable()
export class UsersService {
  private readonly logger = new Logger(UsersService.name);

  constructor(private readonly prisma: PrismaService) {}

  async findAll(
    query: UsersQueryDto,
  ): Promise<BaseResponseDto<UsersListResponseDto>> {
    try {
      const {
        page = 1,
        limit = 10,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      const skip = (page - 1) * limit;

      const total = await this.prisma.user.count();

      const users = await this.prisma.user.findMany({
        skip,
        take: limit,
        select: {
          id: true,
          email: true,
          username: true,
          type: true,
          businessType: true,
          status: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
      });

      const userResponseDtos = users.map(
        (user) =>
          new UserResponseDto({
            id: user.id,
            email: user.email,
            username: user.username,
            type: user.type as any,
            businessType: user.businessType as any,
            status: user.status as any,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          }),
      );

      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      const meta: PaginationMetaDto = {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      };

      const response = new UsersListResponseDto({
        data: userResponseDtos,
        meta,
      });

      return BaseResponseDto.success(response, 'Users retrieved successfully');
    } catch (error) {
      this.logger.error('Error fetching users:', error);
      throw error;
    }
  }
}
