import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PrismaModule } from './prisma/prisma.module';
import { UsersModule } from './users/users.module';
import { EmailModule } from './email/email.module';
import { AuthModule } from './auth/auth.module';
import { BackofficeModule } from './backoffice/backoffice.module';
import { KycModule } from './kyc/kyc.module';
import { ProductsModule } from './products/products.module';
import { WalletModule } from './wallet/wallet.module';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { JobsModule } from './jobs/jobs.module';
import { SubscribeModule } from './subscribe/subscribe.module';
import { CoboModule } from './backoffice/cobo/cobo.module';
import { UsersModule as BackofficeUsersModule } from './backoffice/users/users.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    ScheduleModule.forRoot(),
    PrismaModule,
    UsersModule,
    EmailModule,
    AuthModule,
    BackofficeModule,
    KycModule,
    JobsModule,
    ProductsModule,
    WalletModule,
    SubscribeModule,
    CoboModule,
    BackofficeUsersModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
