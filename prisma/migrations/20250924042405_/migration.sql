-- CreateTable
CREATE TABLE "public"."subscribe" (
    "id" UUID NOT NULL,
    "product_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "wallet_id" UUID NOT NULL,
    "chain_id" UUID NOT NULL,
    "payment_provider" TEXT NOT NULL,
    "payment_status" TEXT NOT NULL,
    "checkout_status" TEXT NOT NULL,
    "subscribe_status" TEXT NOT NULL,
    "amount" DECIMAL(18,8) NOT NULL,
    "expected_price" DECIMAL(18,8) NOT NULL,
    "currencies" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "subscribe_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "public"."subscribe" ADD CONSTRAINT "subscribe_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."product"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeign<PERSON>ey
ALTER TABLE "public"."subscribe" ADD CONSTRAINT "subscribe_wallet_id_fkey" FOREIGN KEY ("wallet_id") REFERENCES "public"."wallet"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."subscribe" ADD CONSTRAINT "subscribe_chain_id_fkey" FOREIGN KEY ("chain_id") REFERENCES "public"."chain"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."subscribe" ADD CONSTRAINT "subscribe_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE CASCADE ON UPDATE CASCADE;
