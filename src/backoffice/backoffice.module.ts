import { Module } from '@nestjs/common';
import { NavManagementModule } from './nav-management/nav-management.module';
import { BackofficeProductsModule } from './products/products.module';
import { SubscribeModule } from './subscribe/subscribe.module';

/**
 * Main backoffice module that aggregates all backoffice functionality
 */
@Module({
  imports: [NavManagementModule, BackofficeProductsModule, SubscribeModule],
  exports: [NavManagementModule, BackofficeProductsModule, SubscribeModule],
})
export class BackofficeModule {}
