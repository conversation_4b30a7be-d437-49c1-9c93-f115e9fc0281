/*
  Warnings:

  - You are about to drop the column `safe_tx_hash` on the `nav_management` table. All the data in the column will be lost.
  - You are about to drop the column `signature` on the `nav_management` table. All the data in the column will be lost.

*/
-- CreateEnum
CREATE TYPE "public"."CoboTxStatus" AS ENUM ('DRAFT', 'SUBMITTED', 'PENDING_SCREENING', 'PENDING_AUTHORIZATION', 'PENDING_SIGNATURE', 'BROADCASTING', 'CONFIRMING', 'COMPLETED', 'FAILED', 'REJECTED', 'PENDING');

-- CreateEnum
CREATE TYPE "public"."CoboTxKind" AS ENUM ('TRANSFER_NATIVE', 'TRANSFER_ERC20', 'TRANSFER_ERC721', 'TRANSFER_ERC1155', 'CONTRACT_CALL', 'APPROVAL_ONLY');

-- AlterTable
ALTER TABLE "public"."nav_management" DROP COLUMN "safe_tx_hash",
DROP COLUMN "signature";

-- CreateTable
CREATE TABLE "public"."cobo_transaction" (
    "id" UUID NOT NULL,
    "subscribeId" UUID,
    "navManagementId" UUID,
    "walletType" TEXT NOT NULL,
    "walletAddress" TEXT NOT NULL,
    "chainId" TEXT NOT NULL,
    "kind" "public"."CoboTxKind" NOT NULL,
    "toAddress" TEXT NOT NULL,
    "method" TEXT,
    "calldata" TEXT,
    "calldataHash" TEXT,
    "decodedParams" JSONB,
    "value" TEXT NOT NULL,
    "tokenAddress" TEXT,
    "amount" TEXT,
    "threshold" INTEGER,
    "owners" JSONB,
    "policyMatched" JSONB,
    "status" "public"."CoboTxStatus" NOT NULL DEFAULT 'DRAFT',
    "coboTransactionId" UUID NOT NULL,
    "safeTxHash" TEXT,
    "onchainTxHash" TEXT,
    "nonce" INTEGER,
    "gasLimit" TEXT,
    "gasPrice" TEXT,
    "maxFeePerGas" TEXT,
    "maxPriorityFee" TEXT,
    "idempotencyKey" TEXT,
    "correlationId" TEXT,
    "createdBy" TEXT,
    "errorCode" TEXT,
    "errorMessage" TEXT,
    "rawRequest" JSONB,
    "rawResponse" JSONB,
    "rawReceipt" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "cobo_transaction_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."cobo_approval" (
    "id" UUID NOT NULL,
    "transactionId" UUID NOT NULL,
    "approver" TEXT NOT NULL,
    "role" TEXT,
    "approvedAt" TIMESTAMP(3) NOT NULL,
    "signature" TEXT,
    "meta" JSONB,

    CONSTRAINT "cobo_approval_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."cobo_webhook_event" (
    "id" UUID NOT NULL,
    "transactionId" UUID,
    "eventType" TEXT NOT NULL,
    "payload" JSONB NOT NULL,
    "deliveredAt" TIMESTAMP(3) NOT NULL,
    "dedupeKey" TEXT,

    CONSTRAINT "cobo_webhook_event_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "cobo_transaction_coboTransactionId_key" ON "public"."cobo_transaction"("coboTransactionId");

-- CreateIndex
CREATE UNIQUE INDEX "cobo_transaction_idempotencyKey_key" ON "public"."cobo_transaction"("idempotencyKey");

-- CreateIndex
CREATE INDEX "cobo_transaction_walletAddress_chainId_idx" ON "public"."cobo_transaction"("walletAddress", "chainId");

-- CreateIndex
CREATE INDEX "cobo_transaction_status_chainId_idx" ON "public"."cobo_transaction"("status", "chainId");

-- CreateIndex
CREATE INDEX "cobo_transaction_onchainTxHash_idx" ON "public"."cobo_transaction"("onchainTxHash");

-- CreateIndex
CREATE INDEX "cobo_transaction_coboTransactionId_idx" ON "public"."cobo_transaction"("coboTransactionId");

-- CreateIndex
CREATE INDEX "cobo_transaction_status_idx" ON "public"."cobo_transaction"("status");

-- CreateIndex
CREATE INDEX "cobo_transaction_subscribeId_idx" ON "public"."cobo_transaction"("subscribeId");

-- CreateIndex
CREATE INDEX "cobo_transaction_navManagementId_idx" ON "public"."cobo_transaction"("navManagementId");

-- CreateIndex
CREATE INDEX "cobo_transaction_createdAt_idx" ON "public"."cobo_transaction"("createdAt");

-- CreateIndex
CREATE INDEX "cobo_approval_transactionId_idx" ON "public"."cobo_approval"("transactionId");

-- CreateIndex
CREATE UNIQUE INDEX "cobo_webhook_event_dedupeKey_key" ON "public"."cobo_webhook_event"("dedupeKey");

-- CreateIndex
CREATE INDEX "cobo_webhook_event_transactionId_idx" ON "public"."cobo_webhook_event"("transactionId");

-- CreateIndex
CREATE INDEX "cobo_webhook_event_eventType_idx" ON "public"."cobo_webhook_event"("eventType");

-- AddForeignKey
ALTER TABLE "public"."cobo_transaction" ADD CONSTRAINT "cobo_transaction_subscribeId_fkey" FOREIGN KEY ("subscribeId") REFERENCES "public"."subscribe"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."cobo_transaction" ADD CONSTRAINT "cobo_transaction_navManagementId_fkey" FOREIGN KEY ("navManagementId") REFERENCES "public"."nav_management"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."cobo_approval" ADD CONSTRAINT "cobo_approval_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "public"."cobo_transaction"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."cobo_webhook_event" ADD CONSTRAINT "cobo_webhook_event_transactionId_fkey" FOREIGN KEY ("transactionId") REFERENCES "public"."cobo_transaction"("coboTransactionId") ON DELETE SET NULL ON UPDATE CASCADE;
