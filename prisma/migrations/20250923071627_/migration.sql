/*
  Warnings:

  - A unique constraint covering the columns `[product_id,tag_id]` on the table `product_tag` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `symbol` to the `chain` table without a default value. This is not possible if the table is not empty.
  - Added the required column `wallet_type` to the `wallet` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "public"."ChainSymbol" AS ENUM ('ETH', 'BNB', 'MATIC', 'AVAX', 'FTM', 'ARB', 'OP');

-- CreateEnum
CREATE TYPE "public"."WalletType" AS ENUM ('METAMASK', 'WALLET_CONNECT', 'SAFE', 'EXTERNAL', 'HARDWARE');

-- AlterTable
ALTER TABLE "public"."chain" ADD COLUMN     "symbol" "public"."ChainSymbol" NOT NULL;

-- AlterTable
ALTER TABLE "public"."nav_management" ADD COLUMN     "manager_nav" TEXT,
ADD COLUMN     "product_id" UUID;

-- AlterTable
ALTER TABLE "public"."supported_network" ADD COLUMN     "img" TEXT;

-- AlterTable
ALTER TABLE "public"."wallet" ADD COLUMN     "wallet_type" "public"."WalletType" NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "product_tag_product_id_tag_id_key" ON "public"."product_tag"("product_id", "tag_id");

-- AddForeignKey
ALTER TABLE "public"."nav_management" ADD CONSTRAINT "nav_management_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "public"."product"("id") ON DELETE SET NULL ON UPDATE CASCADE;
