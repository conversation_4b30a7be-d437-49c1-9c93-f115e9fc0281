import {
  Injectable,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nacl from 'tweetnacl';
import * as crypto from 'crypto';
import { encodeFunctionData } from 'viem';
import {
  WebhookCallbackDto,
  TransactionDataDto,
  WebhookEventDto,
} from './dto/webhook.dto';
import { PrismaService } from '../../prisma/prisma.service';
import { CoboService } from './cobo.service';
import { CoboTxStatus, NavStatus } from '../../../generated/prisma';

@Injectable()
export class CoboWebhookService {
  private readonly logger = new Logger(CoboWebhookService.name);
  private readonly publicKeys: { [key: string]: string };

  constructor(
    private configService: ConfigService,
    private prisma: PrismaService,
    private coboService: CoboService,
  ) {
    // Cobo public keys for signature verification
    this.publicKeys = {
      DEV: 'a04ea1d5fa8da71f1dcfccf972b9c4eba0a2d8aba1f6da26f49977b08a0d2718',
      PROD: '8d4a482641adb2a34b726f05827dba9a9653e5857469b8749052bf4458a86729',
    };
  }

  /**
   * Verify webhook signature according to Cobo documentation
   * @param rawBody Raw request body
   * @param timestamp Request timestamp
   * @param signature Request signature
   * @returns boolean indicating if signature is valid
   */
  verifySignature(
    rawBody: string,
    timestamp: string,
    signature: string,
  ): boolean {
    try {
      const environment = this.configService.get<string>('COBO_ENV', 'DEV');
      const publicKey = this.publicKeys[environment];

      if (!publicKey) {
        this.logger.error(
          `No public key found for environment: ${environment}`,
        );
        return false;
      }

      // Step 1: Concatenate raw body and timestamp
      const message = `${rawBody}|${timestamp}`;

      // Step 2: Compute double SHA-256 hash and convert to hex string
      const sha256Hash = crypto
        .createHash('sha256')
        .update(message, 'utf8')
        .digest();
      const doubleSha256Hash = crypto
        .createHash('sha256')
        .update(sha256Hash)
        .digest('hex');

      // Step 3: Verify signature using nacl (tweetnacl)
      const publicKeyBuffer = Buffer.from(publicKey, 'hex');
      const signatureBuffer = Buffer.from(signature, 'hex');
      const messageBuffer = Buffer.from(doubleSha256Hash, 'hex');

      const isValid = nacl.sign.detached.verify(
        messageBuffer,
        signatureBuffer,
        publicKeyBuffer,
      );

      this.logger.log(`Signature verification result: ${isValid}`);
      return isValid;
    } catch (error) {
      this.logger.error('Error verifying signature:', error);
      return false;
    }
  }

  /**
   * Process webhook event
   * @param event Webhook event data
   */
  async processWebhookEvent(event: WebhookEventDto): Promise<void> {
    this.logger.log(`Processing webhook event: ${event.event_id}`);

    try {
      // First, log the webhook event to the database
      await this.logWebhookEvent(event);

      // Then process the specific event type
      switch (event.data.type) {
        case 'ContractCall':
          await this.handleContractCall(event.data);
          break;
        default:
          this.logger.warn(`Unhandled event type: ${event.type}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing webhook event ${event.event_id}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Log webhook event to database
   * @param event Webhook event data
   */
  private async logWebhookEvent(event: WebhookEventDto): Promise<void> {
    try {
      // Create dedupe key to prevent duplicate events
      const dedupeKey = `${event.event_id}_${event.type}_${event.created_timestamp}`;

      // Extract transaction ID and verify it exists in the database
      let transactionId = null;
      if (event.data?.transaction_id) {
        // Check if the transaction exists in the database
        const existingTransaction =
          await this.prisma.coboTransaction.findUnique({
            where: { id: event.data.transaction_id },
            select: { id: true },
          });

        if (existingTransaction) {
          transactionId = event.data.transaction_id;
        } else {
          this.logger.warn(
            `Transaction ${event.data.transaction_id} not found in database, logging webhook without transaction link`,
          );
        }
      }

      await this.prisma.coboWebhookEvent.create({
        data: {
          transactionId,
          eventType: event.type,
          payload: event as any,
          deliveredAt: new Date(),
          dedupeKey,
        },
      });

      this.logger.log(`Webhook event logged to database: ${event.event_id}`);
    } catch (error) {
      if (error.code === 'P2002') {
        this.logger.warn(`Duplicate webhook event detected: ${event.event_id}`);
      } else {
        this.logger.error(
          `Error logging webhook event to database: ${event.event_id}`,
          error,
        );
      }
    }
  }

  /**
   * Process callback message
   * @param callback Callback data
   * @returns Response indicating approval or rejection
   */
  async processCallback(callback: WebhookCallbackDto): Promise<string> {
    this.logger.log(
      `Processing callback for transaction: ${callback.transaction_id}`,
    );
    try {
      const shouldApprove = await this.shouldApproveTransaction(callback);

      const response = shouldApprove ? 'ok' : 'deny';
      this.logger.log(
        `Callback response for transaction ${callback.transaction_id}: ${response}`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Error processing callback for transaction ${callback.transaction_id}:`,
        error,
      );
      return 'deny';
    }
  }

  /**
   * Handle transaction created event
   */
  private async handleContractCall(event: TransactionDataDto): Promise<void> {
    const transaction = event;
    this.logger.log(`Contract call request_id: ${transaction.request_id}`);

    try {
      // 1. Search transaction by request_id (idempotencyKey) in cobo_transaction table
      const existingTransaction = await this.prisma.coboTransaction.findUnique({
        where: { idempotencyKey: transaction.request_id },
        include: { navManagement: true },
      });

      if (!existingTransaction) {
        this.logger.warn(
          `Transaction with idempotencyKey ${transaction.request_id} not found`,
        );
        return;
      }

      this.logger.log(
        `Found transaction: ${existingTransaction.id} with status: ${existingTransaction.status}`,
      );

      // 2. Map response status to CoboTxStatus enum
      const coboStatus = this.mapToCoboTxStatus(transaction.status);

      // Update CoboTransaction status
      await this.prisma.coboTransaction.update({
        where: { id: existingTransaction.id },
        data: { status: coboStatus },
      });

      this.logger.log(
        `Updated CoboTransaction status from ${existingTransaction.status} to ${coboStatus}`,
      );

      // 3. Update NavManagement status if navManagementId exists
      if (existingTransaction.navManagementId) {
        const navStatus = this.mapToNavStatus(transaction.status);

        if (navStatus) {
          await this.prisma.navManagement.update({
            where: { id: existingTransaction.navManagementId },
            data: { status: navStatus },
          });

          this.logger.log(
            `Updated NavManagement ${existingTransaction.navManagementId} status to ${navStatus}`,
          );
        }
      }

      // 4. Handle fulfill status updates if fulfillId exists
      if (existingTransaction.fulfillId) {
        await this.handleFulfillStatusUpdate(
          existingTransaction.fulfillId,
          coboStatus,
          transaction,
        );
      }

      // 5. Handle settlement status updates if settlementId exists
      if (existingTransaction.settlementId) {
        await this.handleSettlementStatusUpdate(
          existingTransaction.settlementId,
          coboStatus,
          transaction,
        );
      }

      this.logger.log(
        `Successfully processed contract call for transaction ${existingTransaction.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing contract call for request_id ${transaction.request_id}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Map Cobo transaction status string to CoboTxStatus enum
   */
  private mapToCoboTxStatus(status: string): CoboTxStatus {
    const statusMap: { [key: string]: CoboTxStatus } = {
      PendingScreening: CoboTxStatus.PENDING_SCREENING,
      PendingAuthorization: CoboTxStatus.PENDING_AUTHORIZATION,
      PendingSignature: CoboTxStatus.PENDING_SIGNATURE,
      Broadcasting: CoboTxStatus.BROADCASTING,
      Confirming: CoboTxStatus.CONFIRMING,
      Pending: CoboTxStatus.CONFIRMING,
      Completed: CoboTxStatus.COMPLETED,
      Failed: CoboTxStatus.FAILED,
      Rejected: CoboTxStatus.REJECTED,
    };

    return statusMap[status] || CoboTxStatus.DRAFT;
  }

  /**
   * Map Cobo transaction status string to NavStatus enum
   */
  private mapToNavStatus(status: string): NavStatus | null {
    const statusMap: { [key: string]: NavStatus } = {
      PendingScreening: NavStatus.WAITING_FOR_APPROVAL,
      PendingAuthorization: NavStatus.WAITING_FOR_APPROVAL,
      PendingSignature: NavStatus.WAITING_FOR_APPROVAL,
      Broadcasting: NavStatus.WAITING_FOR_POSTING,
      Confirming: NavStatus.POSTING_IN_PROCESS,
      Pending: NavStatus.POSTING_IN_PROCESS,
      Completed: NavStatus.POSTED,
      Failed: NavStatus.POSTING_ERROR,
      Rejected: NavStatus.REJECTED,
    };

    return statusMap[status] || null;
  }

  /**
   * Determine if transaction should be approved
   * This is where you implement your business logic
   */
  private async shouldApproveTransaction(
    callback: WebhookCallbackDto,
  ): Promise<boolean> {
    this.logger.log(
      `Evaluating transaction approval for: ${callback.transaction_id}`,
    );

    try {
      // 1. Check transaction type
      if (callback.type !== 'ContractCall') {
        this.logger.warn(`Unsupported transaction type: ${callback.type}`);
        return false;
      }

      // 2. Check chain support
      if (callback.chain_id !== 'SETH') {
        this.logger.warn(`Unsupported chain: ${callback.chain_id}`);
        return false;
      }

      // 3. Check destination address (example: whitelist check)
      const allowedDestinations = [
        '******************************************',
        '******************************************',
      ];

      if (!allowedDestinations.includes(callback.destination.address)) {
        this.logger.warn(
          `Destination address not in whitelist: ${callback.destination.address}`,
        );
        return false;
      }

      // 4. Check estimated fee (example: reasonable fee check)
      const estimatedFee = parseFloat(callback.fee.estimated_fee_used);
      const maxFee = 0.001;

      if (estimatedFee > maxFee) {
        this.logger.warn(`Estimated fee too high: ${estimatedFee} > ${maxFee}`);
        return false;
      }

      // 7. Check calldata for suspicious patterns
      if (callback.destination.calldata) {
        const suspiciousPatterns = ['0x12345678', '0x87654321'];
        if (
          suspiciousPatterns.some((pattern) =>
            callback.destination.calldata.startsWith(pattern),
          )
        ) {
          this.logger.warn(`Suspicious calldata pattern detected`);
          return false;
        }
      }

      this.logger.log(`Transaction approved: ${callback.transaction_id}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Error evaluating transaction approval: ${error.message}`,
      );
      return false;
    }
  }

  /**
   * Handle fulfill status updates based on Cobo transaction status
   */
  private async handleFulfillStatusUpdate(
    fulfillId: string,
    coboStatus: CoboTxStatus,
    transaction: TransactionDataDto,
  ): Promise<void> {
    try {
      this.logger.log(
        `Processing fulfill status update for fulfillId: ${fulfillId}`,
      );

      const fulfill = await this.prisma.fulfill.findUnique({
        where: { id: fulfillId },
        include: {
          subscribes: true,
        },
      });

      if (!fulfill) {
        this.logger.warn(`Fulfill record with id ${fulfillId} not found`);
        return;
      }

      let newFulfillStatus: NavStatus;
      let notes = fulfill.notes || '';

      // Check if this is setPriceForRequestSubscribe transaction
      const isSetPriceTransaction = transaction.request_id.includes(
        'set-price-subscribe',
      );

      if (isSetPriceTransaction) {
        // Handle setPriceForRequestSubscribe transaction statuses
        switch (coboStatus) {
          case 'COMPLETED':
            newFulfillStatus = 'WAITING_FOR_APPROVAL';
            notes = `SetPrice operation completed successfully. Transaction ID: ${transaction.transaction_id}`;
            await this.triggerFullFillSubscription(fulfill);
            break;

          case 'FAILED':
            newFulfillStatus = 'POSTING_ERROR';
            notes = `SetPrice operation failed. Transaction ID: ${transaction.transaction_id}`;
            break;

          case 'REJECTED':
            newFulfillStatus = 'REJECTED';
            notes = `SetPrice operation rejected. Transaction ID: ${transaction.transaction_id}`;
            break;

          case 'PENDING_SCREENING':
          case 'PENDING_AUTHORIZATION':
          case 'PENDING_SIGNATURE':
          case 'BROADCASTING':
          case 'CONFIRMING':
            newFulfillStatus = 'WAITING_FOR_APPROVAL';
            notes = `SetPrice operation pending approval. Transaction ID: ${transaction.transaction_id}`;
            break;

          default:
            newFulfillStatus = 'WAITING_FOR_APPROVAL';
            notes = `SetPrice status updated to ${coboStatus}. Transaction ID: ${transaction.transaction_id}`;
        }
      } else {
        switch (coboStatus) {
          case 'COMPLETED':
            newFulfillStatus = 'POSTED';
            notes = `FullFillSubscription operation completed successfully. Transaction ID: ${transaction.transaction_id}`;
            break;

          case 'FAILED':
            newFulfillStatus = 'POSTING_ERROR';
            notes = `FullFillSubscription operation failed. Transaction ID: ${transaction.transaction_id}`;
            break;

          case 'REJECTED':
            newFulfillStatus = 'REJECTED';
            notes = `FullFillSubscription operation rejected. Transaction ID: ${transaction.transaction_id}`;
            break;

          case 'PENDING_SCREENING':
          case 'PENDING_AUTHORIZATION':
          case 'PENDING_SIGNATURE':
            newFulfillStatus = 'WAITING_FOR_APPROVAL';
            notes = `FullFillSubscription operation pending approval. Transaction ID: ${transaction.transaction_id}`;
            break;

          case 'BROADCASTING':
          case 'CONFIRMING':
            newFulfillStatus = 'POSTING_IN_PROCESS';
            notes = `FullFillSubscription operation in progress. Transaction ID: ${transaction.transaction_id}`;
            break;

          default:
            newFulfillStatus = 'POSTING_IN_PROCESS';
            notes = `FullFillSubscription status updated to ${coboStatus}. Transaction ID: ${transaction.transaction_id}`;
        }
      }

      await this.prisma.fulfill.update({
        where: { id: fulfillId },
        data: {
          status: newFulfillStatus,
          notes,
        },
      });

      this.logger.log(
        `Updated fulfill ${fulfillId} status to ${newFulfillStatus}`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling fulfill status update for fulfillId ${fulfillId}:`,
        error,
      );
    }
  }

  /**
   * Trigger fullFillSubscription contract call after setPriceForRequestSubscribe is completed
   */
  private async triggerFullFillSubscription(fulfill: any): Promise<void> {
    try {
      this.logger.log(
        `Triggering fullFillSubscription for fulfill ${fulfill.id}`,
      );

      // Get request IDs from subscribes
      const requestIds = fulfill.subscribes
        .filter((sub: any) => sub.requestId)
        .map((sub: any) => sub.requestId);

      if (requestIds.length === 0) {
        this.logger.warn(`No request IDs found for fulfill ${fulfill.id}`);
        return;
      }

      // Generate unique request ID for Cobo transaction
      const requestId = `fulfill-subscription-${fulfill.id}-${crypto.randomUUID()}`;
      const chainId = process.env.CHAIN_ID || '84532';
      const coboWalletId = process.env.COBO_WALLET_ID;
      const coboWalletAddress = process.env.COBO_WALLET_ADDRESS;
      const gatewayContractAddress = process.env.GATEWAY_CONTRACT_ADDRESS;

      if (!coboWalletId || !gatewayContractAddress) {
        throw new InternalServerErrorException(
          'Cobo wallet ID or Gateway contract configuration is missing',
        );
      }

      // Call Cobo fullFillSubscription function
      const coboResult = await this.coboService.createContractCallTransaction({
        request_id: requestId,
        chain_id: chainId,
        source: {
          source_type: 'Org-Controlled',
          wallet_id: coboWalletId,
          address: coboWalletAddress,
        },
        destination: {
          destination_type: 'EVM_Contract',
          address: gatewayContractAddress,
          calldata: this.encodeFullFillSubscription(requestIds),
        },
      });

      // Create CoboTransaction record for fullFillSubscription
      try {
        await this.prisma.coboTransaction.create({
          data: {
            id: requestId,
            fulfillId: fulfill.id,
            walletType: 'Org-Controlled',
            walletAddress: coboWalletAddress,
            chainId: chainId,
            kind: 'CONTRACT_CALL',
            toAddress: gatewayContractAddress,
            method: 'fullFillSubscription',
            calldata: this.encodeFullFillSubscription(requestIds),
            value: '0',
            status: 'SUBMITTED',
            coboTransactionId: coboResult.transaction_id,
            idempotencyKey: requestId,
            rawRequest: {
              request_id: requestId,
              chain_id: chainId,
              source: {
                source_type: 'Org-Controlled',
                wallet_id: coboWalletId,
                address: coboWalletAddress,
              },
              destination: {
                destination_type: 'EVM_Contract',
                address: gatewayContractAddress,
                calldata: this.encodeFullFillSubscription(requestIds),
              },
            },
            rawResponse: coboResult as any,
          },
        });
      } catch (coboTransactionError) {
        this.logger.error(
          'Failed to create CoboTransaction record:',
          coboTransactionError,
        );
      }

      await this.prisma.fulfill.update({
        where: { id: fulfill.id },
        data: {
          notes: `${fulfill.notes || ''}\nFullFillSubscription transaction submitted. Transaction ID: ${coboResult.transaction_id}`,
        },
      });

      this.logger.log(
        `FullFillSubscription transaction submitted for fulfill ${fulfill.id}. Transaction ID: ${coboResult.transaction_id}`,
      );
    } catch (error) {
      this.logger.error(
        `Error triggering fullFillSubscription for fulfill ${fulfill.id}:`,
        error,
      );
    }
  }

  /**
   * Encode fullFillSubscription function call data
   * @param requestIds - Array of request IDs to pass to fullFillSubscription function
   * @returns Encoded function call data
   */
  private encodeFullFillSubscription(requestIds: string[]): string {
    try {
      const formattedRequestIds = requestIds.map((id) => id as `0x${string}`);

      const calldata = encodeFunctionData({
        abi: [
          {
            type: 'function',
            name: 'fullFillSubscription',
            inputs: [{ type: 'bytes32[]', name: 'requestId' }],
            outputs: [],
            stateMutability: 'nonpayable',
          },
        ],
        functionName: 'fullFillSubscription',
        args: [formattedRequestIds],
      });

      return calldata;
    } catch (error) {
      this.logger.error(
        'Failed to encode fullFillSubscription function:',
        error,
      );
      throw new InternalServerErrorException(
        'Failed to encode contract function call',
      );
    }
  }

  /**
   * Handle settlement status updates based on Cobo transaction status
   */
  private async handleSettlementStatusUpdate(
    settlementId: string,
    coboStatus: CoboTxStatus,
    transaction: TransactionDataDto,
  ): Promise<void> {
    try {
      this.logger.log(
        `Processing settlement status update for settlementId: ${settlementId}`,
      );

      const settlement = await this.prisma.settlement.findUnique({
        where: { id: settlementId },
      });

      if (!settlement) {
        this.logger.warn(`Settlement record with id ${settlementId} not found`);
        return;
      }

      let newSettlementStatus: NavStatus;

      switch (coboStatus) {
        case 'COMPLETED':
          newSettlementStatus = 'POSTED';
          this.logger.log(
            `Settlement ${settlementId} completed. Transaction ID: ${transaction.transaction_id}`,
          );
          break;

        case 'FAILED':
          newSettlementStatus = 'POSTING_ERROR';
          this.logger.warn(
            `Settlement ${settlementId} failed. Transaction ID: ${transaction.transaction_id}`,
          );
          break;

        case 'REJECTED':
          newSettlementStatus = 'REJECTED';
          this.logger.warn(
            `Settlement ${settlementId} rejected. Transaction ID: ${transaction.transaction_id}`,
          );
          break;

        case 'PENDING_SCREENING':
        case 'PENDING_AUTHORIZATION':
        case 'PENDING_SIGNATURE':
          newSettlementStatus = 'WAITING_FOR_APPROVAL';
          this.logger.log(
            `Settlement ${settlementId} pending approval. Transaction ID: ${transaction.transaction_id}`,
          );
          break;

        case 'BROADCASTING':
        case 'CONFIRMING':
          newSettlementStatus = 'POSTING_IN_PROCESS';
          this.logger.log(
            `Settlement ${settlementId} posting in process. Transaction ID: ${transaction.transaction_id}`,
          );
          break;

        default:
          newSettlementStatus = 'POSTING_IN_PROCESS';
          this.logger.log(
            `Settlement ${settlementId} status updated to ${coboStatus}. Transaction ID: ${transaction.transaction_id}`,
          );
      }

      await this.prisma.settlement.update({
        where: { id: settlementId },
        data: {
          status: newSettlementStatus,
        },
      });

      this.logger.log(
        `Updated settlement ${settlementId} status to ${newSettlementStatus}`,
      );
    } catch (error) {
      this.logger.error(
        `Error handling settlement status update for settlementId ${settlementId}:`,
        error,
      );
    }
  }
}
