import { IsDateString, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class FulfillRequestDto {
  @ApiProperty({
    description: 'Cutoff date for the fulfill operation (YYYY-MM-DD format)',
    example: '2024-01-15',
  })
  @IsDateString(
    {},
    { message: 'Date must be a valid date in YYYY-MM-DD format' },
  )
  @IsNotEmpty({ message: 'Date is required' })
  date: string;
}
