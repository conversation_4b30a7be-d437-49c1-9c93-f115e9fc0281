import {
  Controller,
  Get,
  Post,
  Query,
  Body,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { SubscribeService } from './subscribe.service';
import {
  SubscribeQueryDto,
  SubscribeListApiResponseDto,
  FulfillRequestDto,
  FulfillApiResponseDto,
  FulfillListQueryDto,
  SettleRequestDto,
  SettleApiResponseDto,
  SettlementListQueryDto,
} from './dto';
import { ReadyToFulfillApiResponseDto } from './dto/ready-to-fulfill-response.dto';
import { ReadyToSettleApiResponseDto } from './dto/ready-to-settle-response.dto';
import { FulfillListApiResponseDto as FulfillListApiResponse } from './dto/fulfill-list.dto';
import { SettlementListApiResponseDto as SettlementListApiResponse } from './dto/settlement-list.dto';
import { BaseBackofficeController } from '../common/base-backoffice.controller';

@ApiTags('Backoffice - Subscribe')
@Controller('backoffice/subscribe-request')
export class SubscribeController extends BaseBackofficeController {
  constructor(private readonly subscribeService: SubscribeService) {
    super();
  }

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get all subscribe requests',
    description:
      'Retrieve paginated list of subscribe requests with filtering and sorting options',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Subscribe requests retrieved successfully',
    schema: {
      example: {
        code: 200,
        status: 'success',
        message: 'Subscribe requests retrieved successfully',
        data: {
          data: [
            {
              id: '123e4567-e89b-12d3-a456-426614174000',
              productName: 'Bitcoin ETF',
              txHash: '0x1234567890abcdef...',
              currency: 'USDC',
              paymentStatus: 'completed',
              checkoutStatus: 'completed',
              subscribeStatus: 'active',
              amount: '100.00000000',
              expectedPrice: '100.00000000',
              createdAt: '2024-01-15T10:30:00.000Z',
            },
          ],
          meta: {
            page: 1,
            limit: 10,
            total: 1,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          },
        },
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page (max 100)',
    example: 10,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    enum: [
      'createdAt',
      'amount',
      'expectedPrice',
      'paymentStatus',
      'checkoutStatus',
      'subscribeStatus',
    ],
    description: 'Field to sort by',
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order',
    example: 'desc',
  })
  @ApiQuery({
    name: 'paymentStatus',
    required: false,
    type: String,
    description: 'Filter by payment status',
    example: 'completed',
  })
  @ApiQuery({
    name: 'checkoutStatus',
    required: false,
    type: String,
    description: 'Filter by checkout status',
    example: 'completed',
  })
  @ApiQuery({
    name: 'subscribeStatus',
    required: false,
    type: String,
    description: 'Filter by subscribe status',
    example: 'active',
  })
  @ApiQuery({
    name: 'chainId',
    required: false,
    type: String,
    description: 'Filter by chain ID',
    example: '1',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
    schema: {
      example: {
        code: 500,
        status: 'error',
        message: 'An error occurred while fetching subscribe requests',
        data: null,
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  async findAll(
    @Query() query: SubscribeQueryDto,
  ): Promise<SubscribeListApiResponseDto> {
    return this.subscribeService.findAll(query);
  }

  @Get('fulfills')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get all fulfill records',
    description:
      'Retrieve paginated list of fulfill records with pricing information from NAV management',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Fulfill records retrieved successfully',
    schema: {
      example: {
        code: 200,
        status: 'success',
        message: 'Fulfill records retrieved successfully',
        data: {
          data: [
            {
              id: '123e4567-e89b-12d3-a456-426614174000',
              productId: '123e4567-e89b-12d3-a456-426614174001',
              productName: 'Bitcoin ETF',
              cutoffDate: '2024-01-15T00:00:00.000Z',
              status: 'POSTED',
              totalAmount: '1000.50000000',
              price: '50000.00',
              totalSubscribe: '50025000.00',
              subscribeCount: 10,
              tokens: [
                {
                  currency: 'USDT',
                  amount: '500.25000000',
                },
                {
                  currency: 'USDC',
                  amount: '500.25000000',
                },
              ],
              createdAt: '2024-01-15T10:30:00.000Z',
              updatedAt: '2024-01-15T10:35:00.000Z',
            },
          ],
          meta: {
            page: 1,
            limit: 10,
            total: 1,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          },
        },
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order',
    example: 'desc',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by status',
    example: 'POSTED',
  })
  @ApiQuery({
    name: 'productId',
    required: false,
    type: String,
    description: 'Filter by product ID',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
    schema: {
      example: {
        code: 500,
        status: 'error',
        message: 'An error occurred while fetching fulfill records',
        data: null,
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  async findAllFulfills(
    @Query() query: FulfillListQueryDto,
  ): Promise<FulfillListApiResponse> {
    return this.subscribeService.findAllFulfills(query);
  }

  @Get('settlements')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get all settlement records',
    description:
      'Retrieve paginated list of settlement records with filtering and sorting options',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Settlement records retrieved successfully',
    schema: {
      example: {
        success: true,
        message: 'Settlement records retrieved successfully',
        data: {
          data: [
            {
              id: '123e4567-e89b-12d3-a456-426614174000',
              settlementDate: '2024-01-15T00:00:00.000Z',
              totalFulfill: 5,
              totalTransaction: 25,
              tokenName: 'USDT',
              amount: '1000.50000000',
              value: '50000.50',
              status: 'POSTED',
              createdAt: '2024-01-15T10:30:00.000Z',
            },
          ],
          meta: {
            page: 1,
            limit: 10,
            total: 1,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          },
        },
      },
    },
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
    example: 'createdAt',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order',
    example: 'desc',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by status',
    example: 'POSTED',
  })
  @ApiQuery({
    name: 'tokenName',
    required: false,
    type: String,
    description: 'Filter by token name',
    example: 'USDT',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
    schema: {
      example: {
        success: false,
        message: 'An error occurred while fetching settlement records',
        data: null,
      },
    },
  })
  async findAllSettlements(
    @Query() query: SettlementListQueryDto,
  ): Promise<SettlementListApiResponse> {
    return this.subscribeService.findAllSettlements(query);
  }

  @Get('ready-to-fulfill')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get ready to fulfill requests',
    description:
      'Retrieve all subscribe requests with PROPOSED checkout status and APPROVED subscribe status, with aggregated summary and detailed request data. Filter by date.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Ready to fulfill requests retrieved successfully',
    schema: {
      example: {
        code: 200,
        status: 'success',
        message: 'Ready to fulfill requests retrieved successfully',
        data: {
          summary: {
            totalAmount: '150000.00',
            numOfTransactions: 25,
            numOfUniqueWallets: 18,
            products: ['Bitcoin ETF', 'Ethereum ETF', 'Solana ETF'],
            token: [
              {
                img: 'https://example.com/bitcoin.jpg',
                displayName: 'Bitcoin ETF',
                tokenAmount: '100.50000000',
                amount: '75000.00',
                percentage: '50.00',
              },
              {
                img: 'https://example.com/ethereum.jpg',
                displayName: 'Ethereum ETF',
                tokenAmount: '50.25000000',
                amount: '45000.00',
                percentage: '30.00',
              },
            ],
          },
          requests: [
            {
              txHash: '0x1234567890abcdef...',
              wallet: '0xabcdef1234567890...',
              product: 'Bitcoin ETF',
              createdAt: '2024-01-15T10:30:00.000Z',
              kytStatus: 'APPROVED',
              token: 'Bitcoin ETF',
              amount: '100.50000000',
              value: '75000.00',
            },
          ],
        },
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
    schema: {
      example: {
        code: 500,
        status: 'error',
        message: 'An error occurred while fetching ready-to-fulfill requests',
        data: null,
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiQuery({
    name: 'date',
    required: true,
    type: String,
    description: 'Filter by date (YYYY-MM-DD format)',
    example: '2024-01-15',
  })
  async getReadyToFulfill(
    @Query('date') date: string,
  ): Promise<ReadyToFulfillApiResponseDto> {
    return this.subscribeService.getReadyToFulfill(date);
  }

  @Get('ready-to-settle')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get ready to settle requests',
    description:
      'Retrieve all fulfill records with POSTED status on a specific date, with aggregated summary and detailed request data. Filter by date.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Ready to settle requests retrieved successfully',
    schema: {
      example: {
        code: 200,
        status: 'success',
        message: 'Ready to settle requests retrieved successfully',
        data: {
          summary: {
            totalAmount: '150000.00',
            numOfTransactions: 25,
            numOfUniqueWallets: 18,
            products: ['Bitcoin ETF', 'Ethereum ETF', 'Solana ETF'],
            token: [
              {
                displayName: 'Bitcoin ETF',
                tokenAmount: '100.50000000',
                amount: '150000.00',
                percentage: '75.5',
              },
            ],
          },
          requests: [
            {
              txHash: '0x1234567890abcdef...',
              walletAddress: '0xabcdef1234567890...',
              productName: 'Bitcoin ETF',
              timestamp: '2024-01-15T10:30:00.000Z',
              status: 'APPROVED',
              token: 'Bitcoin ETF',
              amount: '100.50000000',
              value: '15000.00',
            },
          ],
        },
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
    schema: {
      example: {
        code: 500,
        status: 'error',
        message: 'An error occurred while fetching ready-to-settle requests',
        data: null,
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiQuery({
    name: 'date',
    required: true,
    type: String,
    description: 'Filter by date (YYYY-MM-DD format)',
    example: '2024-01-15',
  })
  async getReadyToSettle(
    @Query('date') date: string,
  ): Promise<ReadyToSettleApiResponseDto> {
    return this.subscribeService.getReadyToSettle(date);
  }

  @Post('fulfill')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Fulfill subscribe requests',
    description:
      'Process fulfill operation for ready-to-fulfill subscribe requests on a specific date. This will call Cobo setPriceForRequestSubscribe function and create fulfill records.',
  })
  @ApiBody({
    type: FulfillRequestDto,
    description: 'Fulfill request data',
    examples: {
      example1: {
        summary: 'Fulfill requests for specific date',
        value: {
          tanggal: '2024-01-15',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Fulfill operation completed successfully',
    schema: {
      example: {
        code: 200,
        status: 'success',
        message: 'Fulfill operation completed successfully',
        data: {
          success: true,
          message: 'Fulfill operation completed successfully',
          data: {
            id: '123e4567-e89b-12d3-a456-426614174000',
            productId: '123e4567-e89b-12d3-a456-426614174001',
            productName: 'Bitcoin ETF',
            cutoffDate: '2024-01-15T14:00:00.000Z',
            subscribeCount: 5,
            totalAmount: '1000.50',
            status: 'SUBMITTED',
            notes:
              'Cobo transaction submitted successfully. Transaction ID: cobo-tx-123',
            coboTransactionId: 'cobo-tx-123',
            createdAt: '2024-01-15T10:30:00.000Z',
            updatedAt: '2024-01-15T10:30:00.000Z',
          },
        },
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Bad request - no ready-to-fulfill subscribes found',
    schema: {
      example: {
        code: 400,
        status: 'error',
        message: 'No ready-to-fulfill subscribes found for the specified date',
        data: null,
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
    schema: {
      example: {
        code: 500,
        status: 'error',
        message: 'An error occurred while processing fulfill request',
        data: null,
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  async fulfill(
    @Body() fulfillRequest: FulfillRequestDto,
  ): Promise<FulfillApiResponseDto | any> {
    return this.subscribeService.fulfill(fulfillRequest);
  }

  @Post('settle')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Settle posted fulfill requests',
    description:
      'Process settlement operation for posted fulfill requests on a specific date. This will call Cobo settleSubscribe function and create settlement records grouped by token.',
  })
  @ApiBody({
    type: SettleRequestDto,
    description: 'Settle request data',
    examples: {
      example1: {
        summary: 'Settle requests for specific date',
        value: {
          date: '2024-01-15',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Settlement operation completed successfully',
    schema: {
      example: {
        code: 200,
        status: 'success',
        message: 'Settlement operation completed successfully',
        data: {
          settlements: [
            {
              id: 'uuid-string',
              settlementDate: '2024-01-15T00:00:00.000Z',
              totalFulfill: 3,
              totalTransaction: 25,
              tokenName: 'USDT',
              tokenAmount: '1000.50000000',
              totalValue: '50000.00',
              status: 'POSTED',
              fulfillIds: [
                'uuid-fulfill-1',
                'uuid-fulfill-2',
                'uuid-fulfill-3',
              ],
              subscribeIds: [
                'uuid-subscribe-1',
                'uuid-subscribe-2',
                'uuid-subscribe-3',
              ],
              createdAt: '2024-01-15T10:30:00.000Z',
            },
          ],
          totalFulfills: 5,
          totalTransactions: 150,
          totalSettlementValue: '250000.00',
        },
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Bad request - no ready-to-settle fulfills found',
    schema: {
      example: {
        code: 400,
        status: 'error',
        message: 'No ready-to-settle fulfills found for the specified date',
        data: null,
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal server error',
    schema: {
      example: {
        code: 500,
        status: 'error',
        message: 'An error occurred while processing settlement request',
        data: null,
        timestamp: '2024-01-15T10:30:00.000Z',
      },
    },
  })
  async settle(
    @Body() settleRequest: SettleRequestDto,
  ): Promise<SettleApiResponseDto> {
    return this.subscribeService.settle(settleRequest);
  }
}
