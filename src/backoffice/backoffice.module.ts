import { Module } from '@nestjs/common';
import { NavManagementModule } from './nav-management/nav-management.module';
import { BackofficeProductsModule } from './products/products.module';
import { SubscribeModule } from './subscribe/subscribe.module';
import { UsersModule } from './users/users.module';

/**
 * Main backoffice module that aggregates all backoffice functionality
 */
@Module({
  imports: [NavManagementModule, BackofficeProductsModule, SubscribeModule, UsersModule],
  exports: [NavManagementModule, BackofficeProductsModule, SubscribeModule, UsersModule],
})
export class BackofficeModule {}
